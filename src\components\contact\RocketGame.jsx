import React, { useState, useEffect, useRef } from "react";
import "./styles/rocketGame.css";

const RocketGame = () => {
  const canvasRef = useRef(null);
  const [score, setScore] = useState(0);
  const [highScore, setHighScore] = useState(0);
  const [gameActive, setGameActive] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  
  // Game state refs to avoid closure issues in event listeners
  const bulletsRef = useRef([]);
  const targetsRef = useRef([]);
  const explosionsRef = useRef([]);
  const scoreRef = useRef(0);
  const gameActiveRef = useRef(false);
  const requestIdRef = useRef(null);
  const lastShotTimeRef = useRef(0);

  // Initialize or reset the game
  const initGame = () => {
    bulletsRef.current = [];
    targetsRef.current = [];
    explosionsRef.current = [];
    scoreRef.current = 0;
    setScore(0);
    setGameActive(true);
    gameActiveRef.current = true;
    
    // Start the game loop
    if (requestIdRef.current) {
      cancelAnimationFrame(requestIdRef.current);
    }
    requestIdRef.current = requestAnimationFrame(gameLoop);
  };

  // Handle mouse movement
  const handleMouseMove = (e) => {
    if (!canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    setMousePosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  // Handle mouse click (shoot)
  const handleClick = () => {
    if (!gameActiveRef.current) return;
    
    const now = Date.now();
    // Limit shooting rate (250ms cooldown)
    if (now - lastShotTimeRef.current > 250) {
      const bullet = {
        x: mousePosition.x,
        y: mousePosition.y,
        vx: 0,
        vy: -5, // Bullet moves upward
        active: true
      };
      
      bulletsRef.current.push(bullet);
      lastShotTimeRef.current = now;
    }
  };

  // Game loop
  const gameLoop = () => {
    if (!gameActiveRef.current) return;
    
    // Update bullets
    bulletsRef.current.forEach((bullet, index) => {
      bullet.y += bullet.vy;
      
      // Remove bullets that go off screen
      if (bullet.y < 0) {
        bullet.active = false;
      }
    });
    
    // Filter out inactive bullets
    bulletsRef.current = bulletsRef.current.filter(bullet => bullet.active);
    
    // Spawn targets randomly
    if (Math.random() < 0.02 && targetsRef.current.length < 5) {
      const canvas = canvasRef.current;
      if (canvas) {
        const target = {
          x: Math.random() * canvas.offsetWidth,
          y: 0,
          vx: (Math.random() - 0.5) * 2,
          vy: 1 + Math.random() * 2,
          active: true
        };
        targetsRef.current.push(target);
      }
    }
    
    // Update targets
    targetsRef.current.forEach(target => {
      target.x += target.vx;
      target.y += target.vy;
      
      const canvas = canvasRef.current;
      if (canvas) {
        // Bounce off walls
        if (target.x < 0 || target.x > canvas.offsetWidth) {
          target.vx *= -1;
        }
        
        // Remove targets that go off bottom
        if (target.y > canvas.offsetHeight) {
          target.active = false;
        }
      }
    });
    
    // Check for collisions
    bulletsRef.current.forEach(bullet => {
      targetsRef.current.forEach(target => {
        if (bullet.active && target.active) {
          const dx = bullet.x - target.x;
          const dy = bullet.y - target.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 20) { // Hit detection radius
            bullet.active = false;
            target.active = false;
            
            // Create explosion
            explosionsRef.current.push({
              x: target.x,
              y: target.y,
              timeCreated: Date.now()
            });
            
            // Increase score
            scoreRef.current += 10;
            setScore(scoreRef.current);
            
            // Update high score if needed
            if (scoreRef.current > highScore) {
              setHighScore(scoreRef.current);
            }
          }
        }
      });
    });
    
    // Filter out inactive targets
    targetsRef.current = targetsRef.current.filter(target => target.active);
    
    // Remove old explosions
    explosionsRef.current = explosionsRef.current.filter(
      explosion => Date.now() - explosion.timeCreated < 500
    );
    
    // Continue the game loop
    requestIdRef.current = requestAnimationFrame(gameLoop);
  };

  // Start/stop game
  const toggleGame = () => {
    if (gameActive) {
      // Stop game
      setGameActive(false);
      gameActiveRef.current = false;
      if (requestIdRef.current) {
        cancelAnimationFrame(requestIdRef.current);
        requestIdRef.current = null;
      }
    } else {
      // Start game
      initGame();
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (requestIdRef.current) {
        cancelAnimationFrame(requestIdRef.current);
      }
    };
  }, []);

  return (
    <div className="rocket-game-container">
      <h3 className="rocket-game-title">Space Defender Mini-Game</h3>
      <p className="rocket-game-description">
        Take a break and have some fun! Move your cursor to control the rocket, 
        click to shoot, and destroy the green targets. How high can you score?
      </p>
      
      <div 
        className="rocket-game-canvas" 
        ref={canvasRef}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
      >
        {/* Rocket that follows cursor */}
        <div 
          className="rocket" 
          style={{ 
            left: `${mousePosition.x}px`, 
            top: `${mousePosition.y}px`,
            transform: `translate(-50%, -50%) rotate(-90deg)` 
          }}
        />
        
        {/* Render bullets */}
        {bulletsRef.current.map((bullet, index) => (
          <div 
            key={`bullet-${index}`}
            className="bullet"
            style={{ left: `${bullet.x}px`, top: `${bullet.y}px` }}
          />
        ))}
        
        {/* Render targets */}
        {targetsRef.current.map((target, index) => (
          <div 
            key={`target-${index}`}
            className="target"
            style={{ left: `${target.x}px`, top: `${target.y}px` }}
          />
        ))}
        
        {/* Render explosions */}
        {explosionsRef.current.map((explosion, index) => (
          <div 
            key={`explosion-${index}`}
            className="explosion"
            style={{ left: `${explosion.x}px`, top: `${explosion.y}px` }}
          />
        ))}
      </div>
      
      <div className="rocket-game-score">Score: {score}</div>
      <div className="rocket-game-high-score">High Score: {highScore}</div>
      
      <div className="rocket-game-controls">
        <button 
          className="rocket-game-button"
          onClick={toggleGame}
        >
          {gameActive ? "Pause Game" : "Start Game"}
        </button>
      </div>
    </div>
  );
};

export default RocketGame;
