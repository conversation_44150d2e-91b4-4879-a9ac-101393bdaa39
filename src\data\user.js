const INFO = {
	main: {
		title: "<PERSON><PERSON><PERSON><PERSON> | Portfolio",
		name: "<PERSON><PERSON><PERSON><PERSON>",
		email: "<EMAIL>",
		logo: "../logo.png",
	},

	socials: {
		github: "https://github.com/Harshith1201",
		linkedin: "https://www.linkedin.com/in/harshith-gangisetty-10071524a//",
		instagram: "https://instagram.com/",
		facebook: "https://facebook.com/",
	},

	homepage: {
		title: "Computer Science student specializing in Cyber Security with a keen interest in AI and network automation.",
		description:
			"I'm a Computer Science student at Amrita Vishwa Vidyapeetham with a focus on Cybersecurity. I have experience in Selenium-based automation, cybersecurity fundamentals, and tech writing. I'm passionate about leveraging emerging technologies to solve real-world problems and continuously expanding my knowledge in the field of cybersecurity and AI.",
	},

	about: {
		title: "I'm <PERSON><PERSON><PERSON><PERSON>, a Computer Science student from Coimbatore, Tamil Nadu.",
		description:
			"I'm currently pursuing my B.Tech in Computer Science Engineering with a concentration in Cybersecurity at Amrita Vishwa Vidyapeetham, expected to graduate in May 2026. With a CGPA of 7.89/10.0, I've focused on coursework including Object-Oriented Programming, Data Structures & Algorithms, Operating Systems, Computer Organization & Architecture, Database Management, and Cryptography. I'm passionate about cybersecurity, AI, and using technology to solve real-world problems. I actively participate in workshops, competitions, and have earned several certifications in areas like networking, prompt engineering, and AI.",
	},

	articles: {
		title: "Sharing insights and knowledge about cybersecurity, AI, and technology.",
		description:
			"A collection of my thoughts, tutorials, and analyses on topics related to cybersecurity, artificial intelligence, and emerging technologies.",
	},

	projects: [
		{
			title: "Malware Threat Intelligence Scrapper",
			description:
				"Developed a real-time threat intelligence system to collect and analyze malware, vulnerability, and phishing data from diverse online sources. Automated scraping, robust data storage, and a web dashboard with API access deliver actionable insights. Leveraged NLP for threat analysis and community validation to ensure reliable threat indicators.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/python/python.png",
			linkText: "View Project",
			link: "https://github.comhttps://github.com/Harshith1201/malware-threat-intelligence-scrapper",
		},

		{
			title: "IOT-based Blood Bank Management System",
			description:
				"Built two React Native apps and a website as a team of three using Augment AI for an IoT-based blood bank system, enabling donor-recipient communication, QR code-based blood safety checks, real-time blood type tracking, donation approvals, emergency storage, user scheduling, blood requests, and donation-based rewards.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/javascript/javascript.png",
			linkText: "View Project",
			link: "https://github.com",
		},

		{
			title: "Human Factors Influencing Password Choices",
			description:
				"Conducted a research study as a team of two to analyze human factors influencing password choices in Tamil Nadu, providing concise insights into usage patterns to inform effective password policy development.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/python/python.png",
			linkText: "View Project",
			link: "https://github.com",
		},

		{
			title: "Implementation of Schnorr Signature",
			description:
				"Worked on the implementation of Schnorr signatures which include signature aggregation verification in a user-friendly simulation.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/html/html.png",
			linkText: "View Project",
			link: "https://github.com",
		},

		{
			title: "CyberReach – Cybersecurity Awareness Platform",
			description:
				"Designed and developed a web platform to promote cybersecurity awareness via blogs, news, and interactive content. Built a Python scraper (BeautifulSoup/Scrapy) to fetch and summarize cybersecurity news from reliable sources.",
			logo: "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/python/python.png",
			linkText: "View Project",
			link: "https://github.com",
		},
	],
};

export default INFO;
